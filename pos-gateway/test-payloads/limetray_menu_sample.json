{"taxes": [{"taxChargeId": "tax1", "taxChargeName": "GST", "taxValue": 18.0, "taxType": 1, "quantityWiseTax": true, "serviceId": "2", "applicableOnItem": true, "chargeTaxes": []}], "charges": [{"taxChargeId": "charge1", "taxChargeName": "Delivery Charge", "taxValue": 25.0, "taxType": 0, "quantityWiseTax": false, "serviceId": "2", "applicableOnItem": false, "chargeTaxes": ["tax1"]}], "categories": [{"categoryId": 1, "categoryName": "Main Course", "categoryDescription": "Delicious main course items", "rank": 1, "categoryImageList": ["https://example.com/main-course.jpg"], "categoryType": "VEG", "serviceIds": ["1", "2", "3"], "isComboEDV": false, "productList": [{"productId": 101, "productType": "1", "rank": 1, "productName": "Margherita Pizza", "productDescription": "Classic pizza with tomato sauce and mozzarella", "productImageList": ["https://example.com/margherita.jpg"], "productTaxes": ["tax1"], "productCharges": [], "productTagList": ["vegetarian", "popular"], "productSkuList": [{"productSkuId": 1001, "outOfStock": false, "productSkuName": "Regular", "productSkuDescription": "Regular size pizza", "productSkuImageList": ["https://example.com/margherita-regular.jpg"], "productSkuPrice": 299.0, "reducedPrice": null, "allergens": ["gluten", "dairy"], "productSKUInfoDTO": {"calories": 250.0, "preparationTime": 15, "serves": 1, "protein": "12g", "carbs": "30g", "fat": "10g", "fiber": "2g", "sodium": "500mg", "totalsugar": "5g", "servingSize": "200g"}, "addons": [{"categoryId": 201, "categoryName": "Extra Toppings", "categoryDescription": "Add extra toppings to your pizza", "rank": 1, "categoryType": "VEG", "min": 0, "max": 3, "isActive": true, "isComboEDV": false, "productList": [{"productId": 301, "productName": "Extra Cheese", "productSkuList": [{"productSkuId": 3001, "outOfStock": false, "productSkuName": "Extra Cheese", "productSkuPrice": 50.0, "productTaxes": ["tax1"], "productCharges": []}]}]}], "services": [{"serviceId": "1", "price": 299.0, "isActive": true}, {"serviceId": "2", "price": 299.0, "isActive": true}], "productTaxes": ["tax1"], "productCharges": []}, {"productSkuId": 1002, "outOfStock": false, "productSkuName": "Large", "productSkuDescription": "Large size pizza", "productSkuPrice": 449.0, "reducedPrice": 399.0, "allergens": ["gluten", "dairy"], "productSKUInfoDTO": {"calories": 400.0, "preparationTime": 20, "serves": 2, "protein": "20g", "carbs": "50g", "fat": "15g"}, "addons": [], "services": [{"serviceId": "1", "price": 449.0, "isActive": true}, {"serviceId": "2", "price": 449.0, "isActive": true}], "productTaxes": ["tax1"], "productCharges": []}]}], "childCategories": [{"categoryId": 2, "categoryName": "Beverages", "categoryDescription": "Refreshing drinks", "rank": 1, "categoryImageList": ["https://example.com/beverages.jpg"], "categoryType": "NONE", "serviceIds": ["1", "2", "3"], "isComboEDV": false, "productList": [{"productId": 201, "productType": null, "rank": 1, "productName": "Coca Cola", "productDescription": "Refreshing cola drink", "productImageList": ["https://example.com/coke.jpg"], "productTaxes": ["tax1"], "productCharges": [], "productTagList": ["cold", "fizzy"], "productSkuList": [{"productSkuId": 2001, "outOfStock": false, "productSkuName": "330ml", "productSkuPrice": 45.0, "allergens": [], "addons": [], "services": [{"serviceId": "1", "price": 45.0, "isActive": true}], "productTaxes": ["tax1"], "productCharges": []}]}], "childCategories": []}]}]}