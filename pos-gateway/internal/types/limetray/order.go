package limetray

// Order represents the LimeTray order structure
// Since LimeTray uses the unified structure directly, we import and re-export the unified order types
import (
	"github.com/nutanalabs/pos-gateway/internal/types/orders"
)

// Order represents the LimeTray order structure (same as unified)
type Order = orders.Order

// OrderInfo represents the LimeTray order info structure (same as unified)
type OrderInfo = orders.OrderInfo

// Payment represents the LimeTray payment structure (same as unified)
type Payment = orders.Payment

// Customer represents the LimeTray customer structure (same as unified)
type Customer = orders.Customer

// OrderItem represents the LimeTray order item structure (same as unified)
type OrderItem = orders.OrderItem

// Tax represents the LimeTray tax structure (same as unified)
type Tax = orders.Tax

// Charge represents the LimeTray charge structure (same as unified)
type Charge = orders.Charge

// Variant represents the LimeTray variant structure (same as unified)
type Variant = orders.Variant

// AddOn represents the LimeTray addon structure (same as unified)
type AddOn = orders.AddOn

// Rider represents the LimeTray rider structure (same as unified)
type Rider = orders.Rider

// OrderResponse represents the LimeTray API response
type OrderResponse struct {
	Message string `json:"message,omitempty"`
	Error   string `json:"error,omitempty"`
	Success bool   `json:"success,omitempty"`
}

// RiderResponse represents the LimeTray rider API response
type RiderResponse struct {
	Message string `json:"message,omitempty"`
	Error   string `json:"error,omitempty"`
	Success bool   `json:"success,omitempty"`
}

// InventoryUpdate represents the LimeTray inventory update structure
type InventoryUpdate struct {
	OutletID        string   `json:"outletId"`
	InStock         bool     `json:"inStock"`
	ItemIDs         []string `json:"itemIds"`
	Type            string   `json:"type"` // "item" or "addon"
	NextAvailableAt string   `json:"nextAvailableAt"`
}

// StoreStatus represents the LimeTray store status structure
type StoreStatus struct {
	OutletID        string `json:"outletId"`
	OrderingEnabled bool   `json:"orderingEnabled"`
	NextAvailableAt string `json:"nextAvailableAt"`
	Reason          string `json:"reason"`
}
