package limetray

// Menu represents the LimeTray menu structure
type Menu struct {
	OutletID   string             `json:"outletId"`
	BrandID    string             `json:"brandId"`
	Taxes      []ProductTaxCharge `json:"taxes"`
	Charges    []ProductTaxCharge `json:"charges"`
	Categories []MenuCategory     `json:"categories"`
}

// ProductTaxCharge represents taxes and charges for items
type ProductTaxCharge struct {
	TaxChargeID      string   `json:"taxChargeId"`
	TaxChargeName    string   `json:"taxChargeName"`
	TaxValue         float64  `json:"taxValue"`
	TaxType          int      `json:"taxType"` // 0 for Flat tax and 1 for Percentage tax
	QuantityWiseTax  bool     `json:"quantityWiseTax"`
	ServiceIDs       []string `json:"serviceIds"`
	DiscountExempted bool     `json:"discountExempted"`
	ApplicableOnItem bool     `json:"applicableOnItem"`
	Taxes            []string `json:"taxes"`
}

// MenuCategory represents a menu category
type MenuCategory struct {
	CategoryID                   int                   `json:"categoryId"`
	CategoryName                 string                `json:"categoryName"`
	CategoryNameLocalized        *LocalizedName        `json:"categoryNameLocalized"`
	CategoryDescription          *string               `json:"categoryDescription"`
	CategoryDescriptionLocalized *LocalizedDescription `json:"categoryDescriptionLocalized"`
	Rank                         int                   `json:"rank"`
	ProductList                  []MenuProduct         `json:"productList"`
	CategoryImageList            []string              `json:"categoryImageList"`
	CategoryType                 string                `json:"categoryType"` // NON-VEG, VEG, NONE
	ChildCategories              []MenuCategory        `json:"childCategories"`
	ServiceIDs                   []string              `json:"serviceIds"`
	IsComboEDV                   bool                  `json:"isComboEDV"`
}

// MenuProduct represents a menu product/item
type MenuProduct struct {
	ProductID                   *int                  `json:"productId"`
	ProductType                 *string               `json:"productType"` // 1 for Veg and 2 for NonVeg
	Rank                        *int                  `json:"rank"`
	ProductName                 *string               `json:"productName"`
	ProductNameLocalized        *LocalizedName        `json:"productNameLocalized"`
	ProductImageList            []string              `json:"productImageList"`
	ProductDescription          *string               `json:"productDescription"`
	ProductDescriptionLocalized *LocalizedDescription `json:"productDescriptionLocalized"`
	ProductSkuList              []MenuProductSKU      `json:"productSkuList"`
	ProductTaxes                []string              `json:"productTaxes"`
	ProductCharges              []string              `json:"productCharges"`
	ProductTagList              []string              `json:"productTagList"`
	ProductSlots                []MenuProductSlots    `json:"productSlots"`
}

// MenuProductSKU represents a product variant/SKU
type MenuProductSKU struct {
	ProductSkuID                   *int                   `json:"productSkuId"`
	OutOfStock                     bool                   `json:"outOfStock"`
	ProductSkuName                 *string                `json:"productSkuName"`
	ProductSkuNameLocalized        *LocalizedName         `json:"productSkuNameLocalized"`
	ProductSkuDescription          *string                `json:"productSkuDescription"`
	ProductSKUDescriptionLocalized *LocalizedDescription  `json:"productSKUDescriptionLocalized"`
	ProductSkuImageList            []string               `json:"productSkuImageList"`
	ProductSkuPrice                *float64               `json:"productSkuPrice"`
	ReducedPrice                   *float64               `json:"reducedPrice"`
	Allergens                      []string               `json:"allergens"`
	ProductSKUInfoDTO              *ProductSKUInfo        `json:"productSKUInfoDTO"`
	Addons                         []ProductAddonTemplate `json:"addons"`
	Services                       []ProductSKUService    `json:"services"`
	ProductTaxes                   []string               `json:"productTaxes"`
	ProductCharges                 []string               `json:"productCharges"`
}

// ProductSKUInfo represents nutrition info for SKU
type ProductSKUInfo struct {
	Calories        *float64 `json:"calories"`
	PreparationTime *int     `json:"preparationTime"`
	Serves          *int     `json:"serves"`
	Allergy         *string  `json:"allergy"`
	ServingSize     *string  `json:"servingSize"`
	Protein         *string  `json:"protein"`
	Carbs           *string  `json:"carbs"`
	Fiber           *string  `json:"fiber"`
	Polydextrose    *string  `json:"polydextrose"`
	Caffeine        *string  `json:"caffeine"`
	Sweetener       *string  `json:"sweetener"`
	MSG             *string  `json:"msg"`
	Fat             *string  `json:"fat"`
	SaturatedFat    *string  `json:"saturatedFat"`
	TransFat        *string  `json:"transFat"`
	Sodium          *string  `json:"sodium"`
	Cholesterol     *string  `json:"cholesterol"`
	Polyols         *string  `json:"polyols"`
	Salt            *string  `json:"salt"`
	TotalSugar      *string  `json:"totalsugar"`
	AddedSugar      *string  `json:"addedsugar"`
}

// ProductAddonTemplate represents addon groups attached to the variant
type ProductAddonTemplate struct {
	CategoryID                   *int                  `json:"categoryId"`
	CategoryName                 *string               `json:"categoryName"`
	CategoryNameLocalized        *LocalizedName        `json:"categoryNameLocalized"`
	CategoryDescription          string                `json:"categoryDescription"`
	CategoryDescriptionLocalized *LocalizedDescription `json:"categoryDescriptionLocalized"`
	Rank                         int                   `json:"rank"`
	ProductList                  []MenuProduct         `json:"productList"`
	CategoryImageList            []string              `json:"categoryImageList"`
	CategoryType                 string                `json:"categoryType"` // NON-VEG, VEG, NONE
	Min                          *int                  `json:"min"`
	Max                          *int                  `json:"max"`
	IsActive                     *bool                 `json:"isActive"`
	IsComboEDV                   bool                  `json:"isComboEDV"`
}

// ProductSKUService represents service wise price and availability status
type ProductSKUService struct {
	ServiceID string   `json:"serviceId"`
	Price     *float64 `json:"price"`
	IsActive  *bool    `json:"isActive"`
}

// MenuProductSlots represents slots for the product
type MenuProductSlots struct {
	BrandSlotID   int             `json:"brandSlotId"`
	BrandSlotName string          `json:"brandSlotName"`
	BrandSlotTime []BrandSlotTime `json:"brandSlotTime"`
	BrandSlotDate []BrandSlotDate `json:"brandSlotDate"`
	WeekDays      []string        `json:"weekDays"`
}

// BrandSlotTime represents time for the slot
type BrandSlotTime struct {
	BrandSlotTimeID int    `json:"brandSlotTimeId"`
	StartTime       string `json:"startTime"`
	EndTime         string `json:"endTime"`
}

// BrandSlotDate represents dates for the slot
type BrandSlotDate struct {
	BrandSlotDateID int `json:"brandSlotDateId"`
	StartDate       int `json:"startDate"`
	EndDate         int `json:"endDate"`
}

// LocalizedName represents language specific names
type LocalizedName map[string]string

// LocalizedDescription represents language specific descriptions
type LocalizedDescription map[string]string
