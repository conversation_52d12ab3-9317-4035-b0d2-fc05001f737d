package handler

import (
	"github.com/gin-gonic/gin"
	"github.com/nutanalabs/pos-gateway/internal/constants"
	"github.com/nutanalabs/pos-gateway/internal/utils"
	logger "github.com/roppenlabs/rapido-logger-go"
)

func (h *Hand<PERSON>) InitRoutes(router *gin.Engine) {
	// petpooja routes
	//h.PetpoojaRoutes(router)
	// urbanpiper routes
	//h.UrbanPiperRoutes(router)

	v1 := router.Group("/api/v1")
	{
		menu := v1.Group("/menu")
		{
			menu.POST("", func(context *gin.Context) {
				clientID := getXConsumerUserName(context)
				if clientID == "" {
					context.JSON(400, gin.H{"error": "Client ID is required"})
					return
				}
				var requestBody map[string]interface{}
				err := context.BindJSON(&requestBody)
				if err != nil {
					logger.Error(logger.Format{
						Message: "Failed to bind JSON request" + err.Error(),
					})
					context.JSON(400, gin.H{"error": "Invalid request"})
					return
				}
				isASyncProcessing := utils.SliceContains(constants.ClientIDsEnabledForAsyncMenuProcessing, clientID)
				if isASyncProcessing {
					go func() {
						_, err := h.PushMenu(requestBody, clientID, isASyncProcessing)
						if err != nil {
							logger.Error(logger.Format{
								Message: err.Message,x
								Data: map[string]string{
									"clientID": clientID,
									"error":    utils.ConvertInterfaceToJSON(err.Data),
								},
							})
							return
						}
					}()
					context.JSON(200, gin.H{"message": "Menu request is queued for processing"})
					return
				} else {
					_, err := h.PushMenu(requestBody, clientID, false)
					if err != nil {
						logger.Error(logger.Format{
							Message: err.Message,
							Data: map[string]string{
								"clientID": clientID,
								"error":    utils.ConvertInterfaceToJSON(err.Data),
							},
						})
						context.JSON(err.HTTPStatus, gin.H{"error": err})
						return
					}
					context.JSON(200, gin.H{"message": "Menu processed successfully"})
				}
			})
		}
		itemStatus := v1.Group("/item-status")
		{
			itemStatus.POST("", h.PushItemInventoryStatus)
		}
		restaurantStatus := v1.Group("/store-status")
		{
			restaurantStatus.POST("", h.PushRestaurantStatus)
		}
		order := v1.Group("/order")
		{
			order.POST("", h.PushOrder)
		}
	}

}
