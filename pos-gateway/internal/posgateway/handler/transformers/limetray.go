package transformers

import (
	"fmt"
	"strconv"
	"strings"

	"github.com/nutanalabs/pos-gateway/internal/constants"
	"github.com/nutanalabs/pos-gateway/internal/types/common"
	"github.com/nutanalabs/pos-gateway/internal/types/limetray"
	"github.com/nutanalabs/pos-gateway/internal/types/orders"
	"github.com/nutanalabs/pos-gateway/internal/utils"
	logger "github.com/roppenlabs/rapido-logger-go"
)

// TransformLimeTrayMenu converts LimeTray menu format to common format
func (t *transformersImpl) TransformLimeTrayMenu(menu *map[string]interface{}) *common.UnifiedMenu {
	var limeTrayMenu limetray.Menu
	err := utils.UnmarshalJSONToInterface(menu, &limeTrayMenu)
	if err != nil {
		logger.Error(logger.Format{
			Message: "Error while unmarshalling LimeTray menu",
			Data: map[string]string{
				"error": utils.ConvertInterfaceToJSON(err),
			},
		})
		return nil
	}

	if len(limeTrayMenu.Categories) == 0 {
		return nil
	}

	// Create lookup maps for charges and taxes using proper struct types
	chargesMap := make(map[string]limetray.ProductTaxCharge)
	taxesMap := make(map[string]limetray.ProductTaxCharge)

	// Build lookup maps using struct-based approach
	for _, charge := range limeTrayMenu.Charges {
		chargesMap[charge.TaxChargeID] = charge
	}
	for _, tax := range limeTrayMenu.Taxes {
		taxesMap[tax.TaxChargeID] = tax
	}

	// Collect all addon groups from products and SKUs using struct-based approach
	addonGroupsMap := make(map[string]limetray.ProductAddonTemplate)
	collectLimeTrayAddonGroups(limeTrayMenu.Categories, addonGroupsMap)

	// Collect all variant groups and variants from SKUs using struct-based approach
	variantGroupsMap := make(map[string]limetray.MenuProductSKU)
	collectLimeTrayVariants(limeTrayMenu.Categories, variantGroupsMap)

	unifiedMenu := &common.UnifiedMenu{
		Restaurant: transformLimeTrayRestaurant(limeTrayMenu.OutletID, limeTrayMenu.BrandID),
		Categories: transformLimeTrayCategories(limeTrayMenu.Categories, chargesMap, taxesMap),
		BillComponents: &common.BillComponents{
			Charges: transformLimeTrayCharges(limeTrayMenu.Charges),
			Taxes:   transformLimeTrayTaxes(limeTrayMenu.Taxes),
		},
		AddOnGroups:   transformLimeTrayAddOnGroupsToRoot(addonGroupsMap),
		VariantGroups: transformLimeTrayVariantGroupsToRoot(variantGroupsMap),
		Variants:      transformLimeTrayVariantsToRoot(variantGroupsMap),
	}

	return unifiedMenu
}

// transformLimeTrayRestaurant transforms LimeTray outlet and brand info to restaurant
func transformLimeTrayRestaurant(outletID, brandID string) *common.Restaurant {
	if outletID == "" {
		return nil
	}

	// Map outletId to restaurant ID
	restaurantID := constants.ProviderAbbreviationMap[constants.LimeTrayProvider] + outletID

	restaurant := &common.Restaurant{
		Id:                   &restaurantID,
		ProviderId:           &outletID,
		Provider:             constants.LimeTrayProvider,
		ProviderAbbreviation: constants.ProviderAbbreviationMap[constants.LimeTrayProvider],
		OrderingEnabled:      true, // Default to true, can be updated based on store status
	}

	// Map brandId if provided
	if brandID != "" {
		restaurant.BrandId = &brandID
	}

	return restaurant
}

// collectLimeTrayAddonGroups recursively collects all addon groups from categories and products
func collectLimeTrayAddonGroups(categories []limetray.MenuCategory, addonGroupsMap map[string]limetray.ProductAddonTemplate) {
	for _, category := range categories {
		// Process products in current category
		for _, product := range category.ProductList {
			// Process SKUs in product
			for _, sku := range product.ProductSkuList {
				// Collect addon groups from SKU
				for _, addon := range sku.Addons {
					if addon.CategoryID != nil {
						key := fmt.Sprintf("%d", *addon.CategoryID)
						addonGroupsMap[key] = addon
					}
				}
			}
		}

		// Recursively process child categories
		if len(category.ChildCategories) > 0 {
			collectLimeTrayAddonGroups(category.ChildCategories, addonGroupsMap)
		}
	}
}

// collectLimeTrayVariants collects all variants from products
func collectLimeTrayVariants(categories []limetray.MenuCategory, variantsMap map[string]limetray.MenuProductSKU) {
	for _, category := range categories {
		// Process products in current category
		for _, product := range category.ProductList {
			// Process SKUs in product - each SKU is essentially a variant
			for _, sku := range product.ProductSkuList {
				if sku.ProductSkuID != nil {
					key := fmt.Sprintf("%d", *sku.ProductSkuID)
					variantsMap[key] = sku
				}
			}
		}

		// Recursively process child categories
		if len(category.ChildCategories) > 0 {
			collectLimeTrayVariants(category.ChildCategories, variantsMap)
		}
	}
}

// transformLimeTrayCategories converts LimeTray categories to common format
func transformLimeTrayCategories(categories []limetray.MenuCategory, chargesMap map[string]limetray.ProductTaxCharge, taxesMap map[string]limetray.ProductTaxCharge) []common.Category {
	if len(categories) == 0 {
		return nil
	}

	commonCategories := make([]common.Category, 0, len(categories))
	for _, category := range categories {
		providerId := fmt.Sprintf("%d", category.CategoryID)
		id := constants.ProviderAbbreviationMap[constants.LimeTrayProvider] + providerId

		// Get first image URL if available
		var imageURL *string
		if len(category.CategoryImageList) > 0 {
			imageURL = &category.CategoryImageList[0]
		}

		commonCategory := common.Category{
			ID:            &id,
			ProviderId:    &providerId,
			Name:          &category.CategoryName,
			Description:   category.CategoryDescription,
			ImageURL:      imageURL,
			SortOrder:     category.Rank,
			Items:         transformLimeTrayProducts(category.ProductList, chargesMap, taxesMap),
			Subcategories: transformLimeTrayCategories(category.ChildCategories, chargesMap, taxesMap),
		}
		commonCategories = append(commonCategories, commonCategory)
	}

	return commonCategories
}

// transformLimeTrayProducts converts LimeTray products to common items
func transformLimeTrayProducts(products []limetray.MenuProduct, chargesMap map[string]limetray.ProductTaxCharge, taxesMap map[string]limetray.ProductTaxCharge) []common.Item {
	if len(products) == 0 {
		return nil
	}

	commonItems := make([]common.Item, 0)
	for _, product := range products {
		// For LimeTray, if a product has multiple SKUs, we create separate items for each SKU
		// If it has only one SKU, we merge the product and SKU data
		if len(product.ProductSkuList) == 0 {
			// Product without SKUs - create a basic item
			item := transformLimeTrayProductToItem(product, nil, chargesMap, taxesMap)
			commonItems = append(commonItems, item)
		} else if len(product.ProductSkuList) == 1 {
			// Product with single SKU - merge product and SKU data
			sku := product.ProductSkuList[0]
			item := transformLimeTrayProductToItem(product, &sku, chargesMap, taxesMap)
			commonItems = append(commonItems, item)
		} else {
			// Product with multiple SKUs - create separate items for each SKU
			for _, sku := range product.ProductSkuList {
				item := transformLimeTrayProductToItem(product, &sku, chargesMap, taxesMap)
				commonItems = append(commonItems, item)
			}
		}
	}

	return commonItems
}

// transformLimeTrayProductToItem converts a LimeTray product (with optional SKU) to common item
func transformLimeTrayProductToItem(product limetray.MenuProduct, sku *limetray.MenuProductSKU, chargesMap map[string]limetray.ProductTaxCharge, taxesMap map[string]limetray.ProductTaxCharge) common.Item {
	var providerId, id string
	var name, description *string
	var price *float64
	var inStock *bool
	var imageURL *string
	var addOnGroupIDs []string
	var variantGroupIDs []string
	var nutritionalInfo *common.NutritionalInfo

	// Use SKU data if available, otherwise use product data
	if sku != nil && sku.ProductSkuID != nil {
		providerId = fmt.Sprintf("%d", *sku.ProductSkuID)
		name = sku.ProductSkuName
		if name == nil {
			name = product.ProductName
		}
		description = sku.ProductSkuDescription
		if description == nil {
			description = product.ProductDescription
		}
		price = sku.ProductSkuPrice
		inStockValue := !sku.OutOfStock
		inStock = &inStockValue

		// Use SKU image if available, otherwise product image
		if len(sku.ProductSkuImageList) > 0 {
			imageURL = &sku.ProductSkuImageList[0]
		} else if len(product.ProductImageList) > 0 {
			imageURL = &product.ProductImageList[0]
		}

		// Note: Charges and taxes are handled at bill component level for LimeTray
		// Individual item charges/taxes are not used in the unified structure

		// Transform addon groups
		addOnGroupIDs = transformLimeTrayAddonGroupIDs(sku.Addons)

		// Transform nutritional info
		if sku.ProductSKUInfoDTO != nil {
			nutritionalInfo = transformLimeTrayNutritionalInfo(sku.ProductSKUInfoDTO)
		}
	} else if product.ProductID != nil {
		providerId = fmt.Sprintf("%d", *product.ProductID)
		name = product.ProductName
		description = product.ProductDescription

		if len(product.ProductImageList) > 0 {
			imageURL = &product.ProductImageList[0]
		}

		// Note: Charges and taxes are handled at bill component level for LimeTray
		// Individual item charges/taxes are not used in the unified structure

		// Default in stock to true for products without SKUs
		inStockValue := true
		inStock = &inStockValue
	} else {
		// Fallback - use category ID or generate a unique ID
		providerId = "unknown"
	}

	id = constants.ProviderAbbreviationMap[constants.LimeTrayProvider] + providerId

	// Transform food type
	var foodType *string
	if product.ProductType != nil {
		foodType = transformLimeTrayFoodType(*product.ProductType)
	}

	// Set sort order
	sortOrder := 0
	if product.Rank != nil {
		sortOrder = *product.Rank
	}

	return common.Item{
		ID:              &id,
		ProviderId:      &providerId,
		Name:            name,
		Description:     description,
		ImageURL:        imageURL,
		Price:           price,
		InStock:         inStock,
		SortOrder:       sortOrder,
		FoodType:        foodType,
		AddOnGroupIDs:   addOnGroupIDs,
		VariantGroupIDs: variantGroupIDs,
		NutritionalInfo: nutritionalInfo,
	}
}

// transformLimeTrayFoodType converts LimeTray food type to common format using constants
func transformLimeTrayFoodType(productType string) *string {
	if foodType, exists := constants.LimeTrayFoodTypesMapToInternal[productType]; exists {
		return &foodType
	}

	// Default to "not specified" if mapping not found using constant
	foodType := constants.FoodTypeNotSpecified
	return &foodType
}

// transformLimeTrayItemCharges converts LimeTray charge IDs to common charges using struct-based approach
func transformLimeTrayItemCharges(chargeIDs []string, chargesMap map[string]limetray.ProductTaxCharge) []common.Charge {
	if len(chargeIDs) == 0 {
		return nil
	}

	charges := make([]common.Charge, 0, len(chargeIDs))
	for _, chargeID := range chargeIDs {
		if charge, exists := chargesMap[chargeID]; exists {
			providerId := charge.TaxChargeID
			id := constants.ProviderAbbreviationMap[constants.LimeTrayProvider] + providerId

			// Use constants for charge type mapping
			chargeType := constants.LimeTrayChargeTypesMapToInternal[charge.TaxType]
			if chargeType == "" {
				chargeType = constants.LimeTrayChargeTypesMapToInternal[constants.LimeTrayTaxTypeFixed] // Default to FIXED using constant
			}

			commonCharge := common.Charge{
				ID:         &id,
				ProviderId: &providerId,
				Name:       &charge.TaxChargeName,
				Value:      &charge.TaxValue,
				Type:       &chargeType,
			}
			charges = append(charges, commonCharge)
		}
	}

	return charges
}

// transformLimeTrayItemTaxes converts LimeTray tax IDs to common taxes using struct-based approach
func transformLimeTrayItemTaxes(taxIDs []string, taxesMap map[string]limetray.ProductTaxCharge) []common.Tax {
	if len(taxIDs) == 0 {
		return nil
	}

	taxes := make([]common.Tax, 0, len(taxIDs))
	for _, taxID := range taxIDs {
		if tax, exists := taxesMap[taxID]; exists {
			providerId := tax.TaxChargeID
			id := constants.ProviderAbbreviationMap[constants.LimeTrayProvider] + providerId

			commonTax := common.Tax{
				ID:         &id,
				ProviderId: &providerId,
				Name:       &tax.TaxChargeName,
				Value:      &tax.TaxValue,
			}
			taxes = append(taxes, commonTax)
		}
	}

	return taxes
}

// transformLimeTrayAddonGroupIDs converts LimeTray addon groups to ID references using struct-based approach
func transformLimeTrayAddonGroupIDs(addons []limetray.ProductAddonTemplate) []string {
	if len(addons) == 0 {
		return nil
	}

	addonGroupIDs := make([]string, 0, len(addons))
	for _, addon := range addons {
		if addon.CategoryID != nil {
			providerId := fmt.Sprintf("%d", *addon.CategoryID)
			id := constants.ProviderAbbreviationMap[constants.LimeTrayProvider] + providerId
			addonGroupIDs = append(addonGroupIDs, id)
		}
	}

	return addonGroupIDs
}

// transformLimeTrayNutritionalInfo converts LimeTray nutritional info to common format
func transformLimeTrayNutritionalInfo(info *limetray.ProductSKUInfo) *common.NutritionalInfo {
	if info == nil {
		return nil
	}

	nutritionalInfo := &common.NutritionalInfo{}

	if info.Calories != nil {
		nutritionalInfo.Calorie = &common.NutritionalValue{
			Value: info.Calories,
			Unit:  utils.StringPtr("kcal"),
		}
	}

	if info.Protein != nil {
		nutritionalInfo.Protein = &common.NutritionalValue{
			Value: parseNutritionalValue(*info.Protein),
			Unit:  utils.StringPtr("g"),
		}
	}

	if info.Carbs != nil {
		nutritionalInfo.Carbohydrate = &common.NutritionalValue{
			Value: parseNutritionalValue(*info.Carbs),
			Unit:  utils.StringPtr("g"),
		}
	}

	if info.Fat != nil {
		nutritionalInfo.TotalFat = &common.NutritionalValue{
			Value: parseNutritionalValue(*info.Fat),
			Unit:  utils.StringPtr("g"),
		}
	}

	if info.Fiber != nil {
		nutritionalInfo.Fiber = &common.NutritionalValue{
			Value: parseNutritionalValue(*info.Fiber),
			Unit:  utils.StringPtr("g"),
		}
	}

	if info.Sodium != nil {
		nutritionalInfo.Sodium = &common.NutritionalValue{
			Value: parseNutritionalValue(*info.Sodium),
			Unit:  utils.StringPtr("mg"),
		}
	}

	if info.TotalSugar != nil {
		nutritionalInfo.TotalSugar = &common.NutritionalValue{
			Value: parseNutritionalValue(*info.TotalSugar),
			Unit:  utils.StringPtr("g"),
		}
	}

	if info.Cholesterol != nil {
		nutritionalInfo.Cholesterol = &common.NutritionalValue{
			Value: parseNutritionalValue(*info.Cholesterol),
			Unit:  utils.StringPtr("mg"),
		}
	}

	if info.Salt != nil {
		nutritionalInfo.Salt = &common.NutritionalValue{
			Value: parseNutritionalValue(*info.Salt),
			Unit:  utils.StringPtr("g"),
		}
	}

	if info.ServingSize != nil {
		nutritionalInfo.ServingInfo = info.ServingSize
	}

	return nutritionalInfo
}

// parseNutritionalValue parses a nutritional value string to float64
func parseNutritionalValue(value string) *float64 {
	if value == "" {
		return nil
	}

	// Remove common units and parse the numeric value
	cleanValue := strings.TrimSpace(value)
	cleanValue = strings.Replace(cleanValue, "g", "", -1)
	cleanValue = strings.Replace(cleanValue, "mg", "", -1)
	cleanValue = strings.Replace(cleanValue, "kcal", "", -1)
	cleanValue = strings.TrimSpace(cleanValue)

	if parsed, err := strconv.ParseFloat(cleanValue, 64); err == nil {
		return &parsed
	}

	return nil
}

// transformLimeTrayCharges converts LimeTray charges to common format using struct-based approach
func transformLimeTrayCharges(charges []limetray.ProductTaxCharge) []common.Charge {
	if len(charges) == 0 {
		return nil
	}

	commonCharges := make([]common.Charge, 0, len(charges))
	for _, charge := range charges {
		providerId := charge.TaxChargeID
		id := constants.ProviderAbbreviationMap[constants.LimeTrayProvider] + providerId

		// Use constants for charge type mapping
		chargeType := constants.LimeTrayChargeTypesMapToInternal[charge.TaxType]
		if chargeType == "" {
			chargeType = constants.LimeTrayChargeTypesMapToInternal[constants.LimeTrayTaxTypeFixed] // Default to FIXED using constant
		}

		commonCharge := common.Charge{
			ID:         &id,
			ProviderId: &providerId,
			Name:       &charge.TaxChargeName,
			Value:      &charge.TaxValue,
			Type:       &chargeType,
		}
		commonCharges = append(commonCharges, commonCharge)
	}

	return commonCharges
}

// transformLimeTrayTaxes converts LimeTray taxes to common format using struct-based approach
func transformLimeTrayTaxes(taxes []limetray.ProductTaxCharge) []common.Tax {
	if len(taxes) == 0 {
		return nil
	}

	commonTaxes := make([]common.Tax, 0, len(taxes))
	for _, tax := range taxes {
		providerId := tax.TaxChargeID
		id := constants.ProviderAbbreviationMap[constants.LimeTrayProvider] + providerId

		commonTax := common.Tax{
			ID:         &id,
			ProviderId: &providerId,
			Name:       &tax.TaxChargeName,
			Value:      &tax.TaxValue,
		}
		commonTaxes = append(commonTaxes, commonTax)
	}

	return commonTaxes
}

// transformLimeTrayAddOnGroupsToRoot transforms addon groups to root level using struct-based approach
func transformLimeTrayAddOnGroupsToRoot(groupsMap map[string]limetray.ProductAddonTemplate) []common.AddOnGroup {
	commonGroups := make([]common.AddOnGroup, 0, len(groupsMap))

	for _, group := range groupsMap {
		if group.CategoryID == nil {
			continue
		}

		providerId := fmt.Sprintf("%d", *group.CategoryID)
		id := constants.ProviderAbbreviationMap[constants.LimeTrayProvider] + providerId

		var minimumNeeded, maximumAllowed *int
		if group.Min != nil {
			minimumNeeded = group.Min
		}
		if group.Max != nil {
			maximumAllowed = group.Max
		}

		commonGroup := common.AddOnGroup{
			ID:             &id,
			ProviderId:     &providerId,
			Name:           group.CategoryName,
			MinimumNeeded:  minimumNeeded,
			MaximumAllowed: maximumAllowed,
			AddOns:         transformLimeTrayAddOns(group.ProductList),
			SortOrder:      group.Rank,
		}
		commonGroups = append(commonGroups, commonGroup)
	}

	return commonGroups
}

// transformLimeTrayAddOns converts LimeTray addon products to common addons using struct-based approach
func transformLimeTrayAddOns(products []limetray.MenuProduct) []common.AddOn {
	if len(products) == 0 {
		return nil
	}

	addOns := make([]common.AddOn, 0)
	for _, product := range products {
		// For addon products, we typically use the first SKU or the product itself
		if len(product.ProductSkuList) > 0 {
			for _, sku := range product.ProductSkuList {
				if sku.ProductSkuID != nil {
					providerId := fmt.Sprintf("%d", *sku.ProductSkuID)
					id := constants.ProviderAbbreviationMap[constants.LimeTrayProvider] + providerId

					name := sku.ProductSkuName
					if name == nil {
						name = product.ProductName
					}

					price := sku.ProductSkuPrice
					if price == nil {
						price = utils.Float64Ptr(0.0)
					}

					inStockValue := !sku.OutOfStock

					// Transform food type using constants
					var foodType *string
					if product.ProductType != nil {
						foodType = transformLimeTrayFoodType(*product.ProductType)
					}

					addOn := common.AddOn{
						ID:         &id,
						ProviderId: &providerId,
						Name:       name,
						Price:      price,
						InStock:    &inStockValue,
						FoodType:   foodType,
					}
					addOns = append(addOns, addOn)
				}
			}
		} else if product.ProductID != nil {
			providerId := fmt.Sprintf("%d", *product.ProductID)
			id := constants.ProviderAbbreviationMap[constants.LimeTrayProvider] + providerId

			inStockValue := true // Default to in stock for products without SKUs

			// Transform food type using constants
			var foodType *string
			if product.ProductType != nil {
				foodType = transformLimeTrayFoodType(*product.ProductType)
			}

			addOn := common.AddOn{
				ID:         &id,
				ProviderId: &providerId,
				Name:       product.ProductName,
				Price:      utils.Float64Ptr(0.0), // Default price
				InStock:    &inStockValue,
				FoodType:   foodType,
			}
			addOns = append(addOns, addOn)
		}
	}

	return addOns
}

// transformLimeTrayVariantGroupsToRoot transforms variant groups to root level using struct-based approach
func transformLimeTrayVariantGroupsToRoot(variantsMap map[string]limetray.MenuProductSKU) []common.VariantGroup {
	// For LimeTray, we create variant groups based on products that have multiple SKUs
	// This follows the same pattern as UrbanPiper and PetPooja using struct-based approach
	variantGroups := make([]common.VariantGroup, 0)

	// Group variants by product (this is a simplified implementation following UrbanPiper pattern)
	productGroups := make(map[string][]string)
	for skuID := range variantsMap {
		// Use a simplified grouping - in practice, you'd need product ID from the SKU
		groupKey := "variants" // Simplified grouping using constants pattern
		productGroups[groupKey] = append(productGroups[groupKey], skuID)
	}

	for groupKey, variantIDs := range productGroups {
		if len(variantIDs) > 1 { // Only create groups with multiple variants
			providerId := groupKey
			id := constants.ProviderAbbreviationMap[constants.LimeTrayProvider] + providerId

			// Transform variant IDs to use full ID format using constants
			fullVariantIDs := make([]string, 0, len(variantIDs))
			for _, variantID := range variantIDs {
				fullID := constants.ProviderAbbreviationMap[constants.LimeTrayProvider] + variantID
				fullVariantIDs = append(fullVariantIDs, fullID)
			}

			variantGroup := common.VariantGroup{
				ID:         &id,
				ProviderId: &providerId,
				Name:       utils.StringPtr("Variants"),
				VariantIDs: fullVariantIDs,
				SortOrder:  0, // Default sort order
			}
			variantGroups = append(variantGroups, variantGroup)
		}
	}

	return variantGroups
}

// transformLimeTrayVariantsToRoot transforms variants to root level using struct-based approach
func transformLimeTrayVariantsToRoot(variantsMap map[string]limetray.MenuProductSKU) []common.Variant {
	variants := make([]common.Variant, 0, len(variantsMap))

	for skuID, sku := range variantsMap {
		providerId := skuID
		id := constants.ProviderAbbreviationMap[constants.LimeTrayProvider] + providerId

		name := sku.ProductSkuName
		if name == nil {
			name = utils.StringPtr("Variant")
		}

		price := sku.ProductSkuPrice
		if price == nil {
			price = utils.Float64Ptr(0.0)
		}

		inStockValue := !sku.OutOfStock

		// Transform nutritional info using struct-based approach
		var nutritionalInfo *common.NutritionalInfo
		if sku.ProductSKUInfoDTO != nil {
			nutritionalInfo = transformLimeTrayNutritionalInfo(sku.ProductSKUInfoDTO)
		}

		// Transform addon group IDs using struct-based approach
		addonGroupIDs := transformLimeTrayAddonGroupIDs(sku.Addons)

		variant := common.Variant{
			ID:              &id,
			ProviderId:      &providerId,
			Name:            name,
			Price:           price,
			InStock:         &inStockValue,
			NutritionalInfo: nutritionalInfo,
			AddOnGroupIDs:   addonGroupIDs,
			SortOrder:       0, // Default sort order
		}
		variants = append(variants, variant)
	}

	return variants
}

// TransformUnifiedOrderToLimeTrayOrder transforms unified order to LimeTray order format
func (t *transformersImpl) TransformUnifiedOrderToLimeTrayOrder(order *orders.Order) interface{} {
	// Since LimeTray uses the unified structure directly, no transformation is needed
	// This follows the same pattern as FreshMenu
	return order
}

// TransformLimeTrayInventory transforms LimeTray inventory update to common format
func (t *transformersImpl) TransformLimeTrayInventory(inventory *map[string]interface{}) *common.InventoryUpdate {
	var limeTrayInventory limetray.InventoryUpdate
	err := utils.UnmarshalJSONToInterface(inventory, &limeTrayInventory)
	if err != nil {
		logger.Error(logger.Format{
			Message: "Error while unmarshalling LimeTray inventory",
			Data: map[string]string{
				"error": utils.ConvertInterfaceToJSON(err),
			},
		})
		return nil
	}

	// Parse NextAvailableAt from string to int64 if needed
	var nextAvailableAt int64
	if limeTrayInventory.NextAvailableAt != "" {
		// For LimeTray, assume it's a Unix timestamp string
		if parsed, err := strconv.ParseInt(limeTrayInventory.NextAvailableAt, 10, 64); err == nil {
			nextAvailableAt = parsed
		}
	}

	commonInventory := &common.InventoryUpdate{
		MenuSharingCode: &limeTrayInventory.OutletID,
		InStock:         limeTrayInventory.InStock,
		NextAvailableAt: nextAvailableAt,
	}

	// Handle different types (addon/item)
	if limeTrayInventory.Type == "addon" {
		commonInventory.AddOns = limeTrayInventory.ItemIDs
	} else {
		commonInventory.Items = limeTrayInventory.ItemIDs
	}

	return commonInventory
}

// TransformLimeTrayStoreStatus transforms LimeTray store status to common format
func (t *transformersImpl) TransformLimeTrayStoreStatus(storeStatus *map[string]interface{}) *common.StoreStatus {
	var limeTrayStoreStatus limetray.StoreStatus
	err := utils.UnmarshalJSONToInterface(storeStatus, &limeTrayStoreStatus)
	if err != nil {
		logger.Error(logger.Format{
			Message: "Error while unmarshalling LimeTray store status",
			Data: map[string]string{
				"error": utils.ConvertInterfaceToJSON(err),
			},
		})
		return nil
	}

	// Parse NextAvailableAt from string to int64 if needed
	var nextAvailableAt int64
	if limeTrayStoreStatus.NextAvailableAt != "" {
		// For LimeTray, assume it's a Unix timestamp string
		if parsed, err := strconv.ParseInt(limeTrayStoreStatus.NextAvailableAt, 10, 64); err == nil {
			nextAvailableAt = parsed
		}
	}

	return &common.StoreStatus{
		MenuSharingCode: limeTrayStoreStatus.OutletID,
		OrderingEnabled: limeTrayStoreStatus.OrderingEnabled,
		NextAvailableAt: nextAvailableAt,
		Reason:          limeTrayStoreStatus.Reason,
	}
}
