package transformers

import (
	"encoding/json"
	"testing"

	"github.com/nutanalabs/pos-gateway/internal/constants"
	"github.com/nutanalabs/pos-gateway/internal/types/orders"
	"github.com/nutanalabs/pos-gateway/internal/utils"
	"github.com/stretchr/testify/assert"
)

func TestTransformLimeTrayMenu(t *testing.T) {
	// Sample LimeTray menu data with outletId and brandId
	limeTrayMenuJSON := `{
		"outletId": "12356",
		"brandId": "8520",
		"taxes": [
			{
				"taxChargeId": "tax1",
				"taxChargeName": "GST",
				"taxValue": 18.0,
				"taxType": 1,
				"quantityWiseTax": true,
				"serviceIds": ["2"],
				"applicableOnItem": true,
				"taxes": []
			}
		],
		"charges": [
			{
				"taxChargeId": "charge1",
				"taxChargeName": "Delivery Charge",
				"taxValue": 25.0,
				"taxType": 0,
				"quantityWiseTax": false,
				"serviceIds": ["2"],
				"applicableOnItem": false,
				"taxes": ["tax1"]
			}
		],
		"categories": [
			{
				"categoryId": 1,
				"categoryName": "Main Course",
				"categoryDescription": "Delicious main course items",
				"rank": 1,
				"categoryImageList": ["https://example.com/main-course.jpg"],
				"categoryType": "VEG",
				"serviceIds": ["1", "2", "3"],
				"isComboEDV": false,
				"productList": [
					{
						"productId": 101,
						"productType": "1",
						"rank": 1,
						"productName": "Margherita Pizza",
						"productDescription": "Classic pizza with tomato sauce and mozzarella",
						"productImageList": ["https://example.com/margherita.jpg"],
						"productTaxes": ["tax1"],
						"productCharges": [],
						"productTagList": ["vegetarian", "popular"],
						"productSkuList": [
							{
								"productSkuId": 1001,
								"outOfStock": false,
								"productSkuName": "Regular",
								"productSkuDescription": "Regular size pizza",
								"productSkuImageList": ["https://example.com/margherita-regular.jpg"],
								"productSkuPrice": 299.0,
								"reducedPrice": null,
								"allergens": ["gluten", "dairy"],
								"productSKUInfoDTO": {
									"calories": 250.0,
									"preparationTime": 15,
									"serves": 1,
									"protein": "12g",
									"carbs": "30g",
									"fat": "10g",
									"fiber": "2g",
									"sodium": "500mg",
									"totalsugar": "5g",
									"servingSize": "200g"
								},
								"addons": [],
								"services": [
									{
										"serviceId": "1",
										"price": 299.0,
										"isActive": true
									}
								],
								"productTaxes": ["tax1"],
								"productCharges": []
							}
						]
					}
				],
				"childCategories": []
			}
		]
	}`

	var menuMap map[string]interface{}
	err := json.Unmarshal([]byte(limeTrayMenuJSON), &menuMap)
	assert.NoError(t, err)

	// Create transformer
	utilsImpl := utils.NewUtils()
	transformer := NewTransformers(utilsImpl)

	// Transform menu
	unifiedMenu := transformer.TransformProviderMenuIntoUnifiedMenu(&menuMap, constants.LimeTrayClientId)

	// Assertions
	assert.NotNil(t, unifiedMenu)
	assert.NotNil(t, unifiedMenu.Categories)
	assert.Len(t, unifiedMenu.Categories, 1)

	// Check restaurant mapping (outletId to restaurant ID)
	assert.NotNil(t, unifiedMenu.Restaurant)
	assert.Equal(t, "12356", *unifiedMenu.Restaurant.ProviderId)
	assert.Equal(t, "12356", *unifiedMenu.Restaurant.MenuSharingCode)
	assert.Equal(t, constants.LimeTrayProvider, unifiedMenu.Restaurant.Provider)
	assert.Equal(t, constants.ProviderAbbreviationMap[constants.LimeTrayProvider], unifiedMenu.Restaurant.ProviderAbbreviation)
	assert.True(t, unifiedMenu.Restaurant.OrderingEnabled)

	// Check brandId mapping
	assert.NotNil(t, unifiedMenu.BrandId)
	assert.Equal(t, "8520", *unifiedMenu.BrandId)

	// Check category transformation
	category := unifiedMenu.Categories[0]
	assert.Equal(t, "LT1", *category.ID)
	assert.Equal(t, "1", *category.ProviderId)
	assert.Equal(t, "Main Course", *category.Name)
	assert.Equal(t, "Delicious main course items", *category.Description)
	assert.Equal(t, 1, category.SortOrder)

	// Check items transformation
	assert.Len(t, category.Items, 1)
	item := category.Items[0]
	assert.Equal(t, "LT1001", *item.ID)
	assert.Equal(t, "1001", *item.ProviderId)
	assert.Equal(t, "Regular", *item.Name)
	assert.Equal(t, 299.0, *item.Price)
	assert.True(t, *item.InStock)
	assert.Equal(t, "veg", *item.FoodType)

	// Check nutritional info
	assert.NotNil(t, item.NutritionalInfo)
	assert.NotNil(t, item.NutritionalInfo.Calorie)
	assert.Equal(t, 250.0, *item.NutritionalInfo.Calorie.Value)
	assert.Equal(t, "kcal", *item.NutritionalInfo.Calorie.Unit)

	// Check bill components
	assert.NotNil(t, unifiedMenu.BillComponents)
	assert.Len(t, unifiedMenu.BillComponents.Charges, 1)
	assert.Len(t, unifiedMenu.BillComponents.Taxes, 1)

	charge := unifiedMenu.BillComponents.Charges[0]
	assert.Equal(t, "LTcharge1", *charge.ID)
	assert.Equal(t, "charge1", *charge.ProviderId)
	assert.Equal(t, "Delivery Charge", *charge.Name)
	assert.Equal(t, 25.0, *charge.Value)
	assert.Equal(t, "FIXED", *charge.Type)

	tax := unifiedMenu.BillComponents.Taxes[0]
	assert.Equal(t, "LTtax1", *tax.ID)
	assert.Equal(t, "tax1", *tax.ProviderId)
	assert.Equal(t, "GST", *tax.Name)
	assert.Equal(t, 18.0, *tax.Value)
}

func TestTransformLimeTrayFoodType(t *testing.T) {
	tests := []struct {
		input    string
		expected string
	}{
		{"1", "veg"},
		{"2", "non veg"},
		{"unknown", "not specified"},
	}

	for _, test := range tests {
		result := transformLimeTrayFoodType(test.input)
		assert.Equal(t, test.expected, *result)
	}
}

func TestTransformLimeTrayMenuWithNestedVariantsAndAddons(t *testing.T) {
	// Sample LimeTray menu data with nested variants and addons
	limeTrayMenuJSON := `{
		"outletId": "12356",
		"brandId": "8520",
		"taxes": [],
		"charges": [],
		"categories": [
			{
				"categoryId": 1,
				"categoryName": "Pizza",
				"rank": 1,
				"categoryImageList": [],
				"categoryType": "VEG",
				"serviceIds": ["1"],
				"isComboEDV": false,
				"productList": [
					{
						"productId": 101,
						"productType": "1",
						"rank": 1,
						"productName": "Build Your Own Pizza",
						"productSkuList": [
							{
								"productSkuId": 1001,
								"outOfStock": false,
								"productSkuName": "Small",
								"productSkuPrice": 299.0,
								"addons": [
									{
										"categoryId": 2001,
										"categoryName": "Toppings",
										"min": 0,
										"max": 5,
										"productList": [
											{
												"productId": 3001,
												"productName": "Extra Cheese",
												"productSkuList": [
													{
														"productSkuId": 4001,
														"productSkuName": "Regular Cheese",
														"productSkuPrice": 50.0,
														"outOfStock": false,
														"addons": []
													}
												]
											}
										]
									}
								]
							},
							{
								"productSkuId": 1002,
								"outOfStock": false,
								"productSkuName": "Large",
								"productSkuPrice": 399.0,
								"addons": []
							}
						]
					}
				],
				"childCategories": []
			}
		]
	}`

	var menuMap map[string]interface{}
	err := json.Unmarshal([]byte(limeTrayMenuJSON), &menuMap)
	assert.NoError(t, err)

	// Create transformer
	utilsImpl := utils.NewUtils()
	transformer := NewTransformers(utilsImpl)

	// Transform menu
	unifiedMenu := transformer.TransformProviderMenuIntoUnifiedMenu(&menuMap, constants.LimeTrayClientId)

	// Assertions
	assert.NotNil(t, unifiedMenu)

	// Check that addon groups are collected properly
	assert.NotNil(t, unifiedMenu.AddOnGroups)
	assert.Len(t, unifiedMenu.AddOnGroups, 1)

	addonGroup := unifiedMenu.AddOnGroups[0]
	assert.Equal(t, "LT2001", *addonGroup.ID)
	assert.Equal(t, "2001", *addonGroup.ProviderId)
	assert.Equal(t, "Toppings", *addonGroup.Name)
	assert.Equal(t, 0, *addonGroup.MinimumNeeded)
	assert.Equal(t, 5, *addonGroup.MaximumAllowed)
	assert.Len(t, addonGroup.AddOns, 1)

	addon := addonGroup.AddOns[0]
	assert.Equal(t, "LT4001", *addon.ID)
	assert.Equal(t, "4001", *addon.ProviderId)
	assert.Equal(t, "Regular Cheese", *addon.Name)
	assert.Equal(t, 50.0, *addon.Price)

	// Check that variants are collected properly
	assert.NotNil(t, unifiedMenu.Variants)
	assert.Len(t, unifiedMenu.Variants, 3) // 2 main variants + 1 addon variant

	// Check variant groups - should have one group for the pizza sizes (Small and Large)
	assert.NotNil(t, unifiedMenu.VariantGroups)
	// Should have one variant group for the pizza sizes since we have 2 variants with similar names
	if len(unifiedMenu.VariantGroups) > 0 {
		variantGroup := unifiedMenu.VariantGroups[0]
		assert.NotNil(t, variantGroup.ID)
		assert.NotNil(t, variantGroup.Name)
		assert.GreaterOrEqual(t, len(variantGroup.VariantIDs), 2)
	}
}

func TestTransformUnifiedOrderToLimeTrayOrder(t *testing.T) {
	utilsImpl := utils.NewUtils()
	transformer := NewTransformers(utilsImpl)

	// Create a sample unified order
	order := &orders.Order{
		OrderInfo: orders.OrderInfo{
			OrderID: "test-order-123",
			Total:   500.0,
		},
	}

	// Transform order
	result := transformer.TransformUnifiedOrderToProviderOrder(order, constants.LimeTrayClientId)

	// Since LimeTray uses unified structure directly, it should return the same order
	assert.Equal(t, order, result)
}
