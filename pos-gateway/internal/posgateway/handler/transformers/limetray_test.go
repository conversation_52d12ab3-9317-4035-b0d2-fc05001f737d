package transformers

import (
	"encoding/json"
	"testing"

	"github.com/nutanalabs/pos-gateway/internal/constants"
	"github.com/nutanalabs/pos-gateway/internal/types/orders"
	"github.com/nutanalabs/pos-gateway/internal/utils"
	"github.com/stretchr/testify/assert"
)

func TestTransformLimeTrayMenu(t *testing.T) {
	// Sample LimeTray menu data based on the actual sample request body
	limeTrayMenuJSON := `{
		"outletId": "12356",
		"brandId": "8520",
		"taxes": [
			{
				"taxChargeId": "tax1",
				"taxChargeName": "GST",
				"taxValue": 18.0,
				"taxType": 1,
				"quantityWiseTax": true,
				"serviceId": "2",
				"applicableOnItem": true,
				"chargeTaxes": []
			}
		],
		"charges": [
			{
				"taxChargeId": "charge1",
				"taxChargeName": "Delivery Charge",
				"taxValue": 25.0,
				"taxType": 0,
				"quantityWiseTax": false,
				"serviceId": "2",
				"applicableOnItem": false,
				"chargeTaxes": ["tax1"]
			}
		],
		"categories": [
			{
				"categoryId": 1,
				"categoryName": "Main Course",
				"categoryDescription": "Delicious main course items",
				"rank": 1,
				"categoryImageList": ["https://example.com/main-course.jpg"],
				"categoryType": "VEG",
				"serviceIds": ["1", "2", "3"],
				"isComboEDV": false,
				"productList": [
					{
						"productId": 101,
						"productType": "1",
						"rank": 1,
						"productName": "Margherita Pizza",
						"productDescription": "Classic pizza with tomato sauce and mozzarella",
						"productImageList": ["https://example.com/margherita.jpg"],
						"productTaxes": ["tax1"],
						"productCharges": [],
						"productTagList": ["vegetarian", "popular"],
						"productSkuList": [
							{
								"productSkuId": 1001,
								"outOfStock": false,
								"productSkuName": "Regular",
								"productSkuDescription": "Regular size pizza",
								"productSkuImageList": ["https://example.com/margherita-regular.jpg"],
								"productSkuPrice": 299.0,
								"reducedPrice": null,
								"allergens": ["gluten", "dairy"],
								"productSKUInfoDTO": {
									"calories": 250.0,
									"preparationTime": 15,
									"serves": 1,
									"protein": "12g",
									"carbs": "30g",
									"fat": "10g",
									"fiber": "2g",
									"sodium": "500mg",
									"totalsugar": "5g",
									"servingSize": "200g"
								},
								"addons": [],
								"services": [
									{
										"serviceId": "1",
										"price": 299.0,
										"isActive": true
									}
								],
								"productTaxes": ["tax1"],
								"productCharges": []
							}
						]
					}
				],
				"childCategories": []
			}
		]
	}`

	var menuMap map[string]interface{}
	err := json.Unmarshal([]byte(limeTrayMenuJSON), &menuMap)
	assert.NoError(t, err)

	// Create transformer
	utilsImpl := utils.NewUtils()
	transformer := NewTransformers(utilsImpl)

	// Transform menu
	unifiedMenu := transformer.TransformProviderMenuIntoUnifiedMenu(&menuMap, constants.LimeTrayClientId)

	// Assertions
	assert.NotNil(t, unifiedMenu)
	assert.NotNil(t, unifiedMenu.Categories)
	assert.Len(t, unifiedMenu.Categories, 1)

	// Check restaurant transformation - before unified processing
	assert.NotNil(t, unifiedMenu.Restaurant)
	assert.Equal(t, "12356", *unifiedMenu.Restaurant.Id) // Initially set as outletID
	assert.Equal(t, "8520", *unifiedMenu.Restaurant.BrandId)
	assert.Equal(t, constants.LimeTrayProvider, unifiedMenu.Restaurant.Provider)
	assert.True(t, unifiedMenu.Restaurant.OrderingEnabled)

	// Check category transformation
	category := unifiedMenu.Categories[0]
	assert.Equal(t, "LT1", *category.ID)
	assert.Equal(t, "1", *category.ProviderId)
	assert.Equal(t, "Main Course", *category.Name)
	assert.Equal(t, "Delicious main course items", *category.Description)
	assert.Equal(t, 1, category.SortOrder)

	// Check items transformation
	assert.Len(t, category.Items, 1)
	item := category.Items[0]
	assert.Equal(t, "LT1001", *item.ID)
	assert.Equal(t, "1001", *item.ProviderId)
	assert.Equal(t, "Regular", *item.Name)
	assert.Equal(t, 299.0, *item.Price)
	assert.True(t, *item.InStock)
	assert.Equal(t, "veg", *item.FoodType)

	// Check nutritional info
	assert.NotNil(t, item.NutritionalInfo)
	assert.NotNil(t, item.NutritionalInfo.Calorie)
	assert.Equal(t, 250.0, *item.NutritionalInfo.Calorie.Value)
	assert.Equal(t, "kcal", *item.NutritionalInfo.Calorie.Unit)

	// Check bill components
	assert.NotNil(t, unifiedMenu.BillComponents)
	assert.Len(t, unifiedMenu.BillComponents.Charges, 1)
	assert.Len(t, unifiedMenu.BillComponents.Taxes, 1)

	charge := unifiedMenu.BillComponents.Charges[0]
	assert.Equal(t, "LTcharge1", *charge.ID)
	assert.Equal(t, "charge1", *charge.ProviderId)
	assert.Equal(t, "Delivery Charge", *charge.Name)
	assert.Equal(t, 25.0, *charge.Value)
	assert.Equal(t, "FIXED", *charge.Type)

	tax := unifiedMenu.BillComponents.Taxes[0]
	assert.Equal(t, "LTtax1", *tax.ID)
	assert.Equal(t, "tax1", *tax.ProviderId)
	assert.Equal(t, "GST", *tax.Name)
	assert.Equal(t, 18.0, *tax.Value)
}

func TestTransformLimeTrayFoodType(t *testing.T) {
	tests := []struct {
		input    string
		expected string
	}{
		{"1", "veg"},
		{"2", "non veg"},
		{"unknown", "not Specified"},
	}

	for _, test := range tests {
		result := transformLimeTrayFoodType(test.input)
		assert.Equal(t, test.expected, *result)
	}
}

func TestTransformLimeTrayMenuWithActualSample(t *testing.T) {
	// Sample LimeTray menu data based on the actual sample request body provided
	limeTrayMenuJSON := `{
		"outletId": "12356",
		"brandId": "8520",
		"categories": [
			{
				"categoryId": 276524,
				"categoryName": "Pizzas - Veg",
				"categoryNameLocalized": {},
				"categoryDescription": null,
				"categoryDescriptionLocalized": {},
				"rank": 13,
				"productList": [
					{
						"productId": 123456,
						"productType": "1",
						"rank": 1,
						"productName": "Margherita Pizza",
						"productNameLocalized": {},
						"productImageList": ["https://example.com/margherita.jpg"],
						"productDescription": "Classic pizza with tomato sauce and mozzarella",
						"productDescriptionLocalized": {},
						"productSkuList": [
							{
								"productSkuId": 789012,
								"outOfStock": false,
								"productSkuName": "Regular",
								"productSkuNameLocalized": {},
								"productSkuDescription": "Regular size pizza",
								"productSKUDescriptionLocalized": {},
								"productSkuImageList": ["https://example.com/margherita-regular.jpg"],
								"productSkuPrice": 299.0,
								"reducedPrice": null,
								"allergens": ["gluten", "dairy"],
								"productSKUInfoDTO": {
									"calories": 250.0,
									"preparationTime": 15,
									"serves": 1,
									"protein": "12g",
									"carbs": "30g",
									"fat": "10g"
								},
								"addons": [],
								"services": [],
								"productTaxes": ["6013_0_2.5", "6012_0_2.5"],
								"productCharges": ["12743_1_7.0"]
							}
						],
						"productTaxes": ["6013_0_2.5", "6012_0_2.5"],
						"productCharges": ["12743_1_7.0"],
						"productTagList": ["vegetarian", "popular"],
						"productSlots": []
					}
				],
				"categoryImageList": [],
				"categoryType": "NONE",
				"childCategories": [],
				"serviceIds": ["1", "2", "3"],
				"isComboEDV": false
			},
			{
				"categoryId": 233018,
				"categoryName": "Sides",
				"categoryNameLocalized": {"en": "Sides"},
				"categoryDescription": null,
				"categoryDescriptionLocalized": {"en": null},
				"rank": 18,
				"productList": [],
				"categoryImageList": [],
				"categoryType": "NONE",
				"childCategories": [],
				"serviceIds": ["1", "2", "3"],
				"isComboEDV": false
			}
		],
		"taxes": [
			{
				"taxChargeId": "6013_0_2.5",
				"taxChargeName": "CGST",
				"taxValue": 2.5,
				"taxType": 0,
				"quantityWiseTax": false,
				"serviceIds": ["2", "1"],
				"discountExempted": true,
				"applicableOnItem": false
			},
			{
				"taxChargeId": "6012_0_2.5",
				"taxChargeName": "SGST",
				"taxValue": 2.5,
				"taxType": 0,
				"quantityWiseTax": false,
				"serviceIds": ["2", "1"],
				"discountExempted": true,
				"applicableOnItem": false
			}
		],
		"charges": [
			{
				"taxChargeId": "12743_1_7.0",
				"taxChargeName": "Packaging Charges",
				"taxValue": 7.0,
				"taxType": 1,
				"quantityWiseTax": false,
				"serviceIds": ["2", "1"],
				"discountExempted": true,
				"applicableOnItem": false,
				"taxes": []
			}
		]
	}`

	var menuMap map[string]interface{}
	err := json.Unmarshal([]byte(limeTrayMenuJSON), &menuMap)
	assert.NoError(t, err)

	// Create transformer
	utilsImpl := utils.NewUtils()
	transformer := NewTransformers(utilsImpl)

	// Transform menu
	unifiedMenu := transformer.TransformProviderMenuIntoUnifiedMenu(&menuMap, constants.LimeTrayClientId)

	// Assertions
	assert.NotNil(t, unifiedMenu)

	// Check restaurant transformation - before unified processing
	assert.NotNil(t, unifiedMenu.Restaurant)
	assert.Equal(t, "12356", *unifiedMenu.Restaurant.Id) // Initially set as outletID
	assert.Equal(t, "8520", *unifiedMenu.Restaurant.BrandId)
	assert.Equal(t, constants.LimeTrayProvider, unifiedMenu.Restaurant.Provider)
	assert.True(t, unifiedMenu.Restaurant.OrderingEnabled)

	// Check categories
	assert.NotNil(t, unifiedMenu.Categories)
	assert.Len(t, unifiedMenu.Categories, 2)

	// Check first category (Pizzas - Veg)
	category1 := unifiedMenu.Categories[0]
	assert.Equal(t, "LT276524", *category1.ID)
	assert.Equal(t, "276524", *category1.ProviderId)
	assert.Equal(t, "Pizzas - Veg", *category1.Name)
	assert.Equal(t, 13, category1.SortOrder)
	assert.Len(t, category1.Items, 1)

	// Check item in first category
	item := category1.Items[0]
	assert.Equal(t, "LT789012", *item.ID)
	assert.Equal(t, "789012", *item.ProviderId)
	assert.Equal(t, "Regular", *item.Name)
	assert.Equal(t, 299.0, *item.Price)
	assert.True(t, *item.InStock)
	assert.Equal(t, "veg", *item.FoodType)

	// Check second category (Sides)
	category2 := unifiedMenu.Categories[1]
	assert.Equal(t, "LT233018", *category2.ID)
	assert.Equal(t, "233018", *category2.ProviderId)
	assert.Equal(t, "Sides", *category2.Name)
	assert.Equal(t, 18, category2.SortOrder)
	assert.Len(t, category2.Items, 0) // Empty product list

	// Check bill components
	assert.NotNil(t, unifiedMenu.BillComponents)
	assert.Len(t, unifiedMenu.BillComponents.Taxes, 2)
	assert.Len(t, unifiedMenu.BillComponents.Charges, 1)

	// Check taxes
	cgst := unifiedMenu.BillComponents.Taxes[0]
	assert.Equal(t, "LT6013_0_2.5", *cgst.ID)
	assert.Equal(t, "6013_0_2.5", *cgst.ProviderId)
	assert.Equal(t, "CGST", *cgst.Name)
	assert.Equal(t, 2.5, *cgst.Value)

	sgst := unifiedMenu.BillComponents.Taxes[1]
	assert.Equal(t, "LT6012_0_2.5", *sgst.ID)
	assert.Equal(t, "6012_0_2.5", *sgst.ProviderId)
	assert.Equal(t, "SGST", *sgst.Name)
	assert.Equal(t, 2.5, *sgst.Value)

	// Check charges
	packagingCharge := unifiedMenu.BillComponents.Charges[0]
	assert.Equal(t, "LT12743_1_7.0", *packagingCharge.ID)
	assert.Equal(t, "12743_1_7.0", *packagingCharge.ProviderId)
	assert.Equal(t, "Packaging Charges", *packagingCharge.Name)
	assert.Equal(t, 7.0, *packagingCharge.Value)
	assert.Equal(t, "PERCENTAGE", *packagingCharge.Type) // taxType 1 = PERCENTAGE
}

func TestLimeTrayTransformationFollowsStructBasedPattern(t *testing.T) {
	// Test that LimeTray transformation follows the same struct-based pattern as UrbanPiper and PetPooja
	limeTrayMenuJSON := `{
		"outletId": "12356",
		"brandId": "8520",
		"categories": [
			{
				"categoryId": 276524,
				"categoryName": "Test Category",
				"rank": 1,
				"productList": [],
				"childCategories": []
			}
		],
		"taxes": [
			{
				"taxChargeId": "tax1",
				"taxChargeName": "GST",
				"taxValue": 18.0,
				"taxType": 1
			}
		],
		"charges": [
			{
				"taxChargeId": "charge1",
				"taxChargeName": "Delivery Charge",
				"taxValue": 25.0,
				"taxType": 0
			}
		]
	}`

	var menuMap map[string]interface{}
	err := json.Unmarshal([]byte(limeTrayMenuJSON), &menuMap)
	assert.NoError(t, err)

	utilsImpl := utils.NewUtils()
	transformer := NewTransformers(utilsImpl)

	// Transform menu using struct-based approach
	unifiedMenu := transformer.TransformProviderMenuIntoUnifiedMenu(&menuMap, constants.LimeTrayClientId)

	// Verify struct-based transformation results
	assert.NotNil(t, unifiedMenu)

	// Test restaurant mapping using constants - before unified processing
	assert.NotNil(t, unifiedMenu.Restaurant)
	assert.Equal(t, "12356", *unifiedMenu.Restaurant.Id) // Initially set as outletID
	assert.Equal(t, "8520", *unifiedMenu.Restaurant.BrandId)
	assert.Equal(t, constants.LimeTrayProvider, unifiedMenu.Restaurant.Provider)

	// Test charge type mapping using constants
	assert.NotNil(t, unifiedMenu.BillComponents)
	assert.NotNil(t, unifiedMenu.BillComponents.Charges)
	assert.Len(t, unifiedMenu.BillComponents.Charges, 1)
	charge := unifiedMenu.BillComponents.Charges[0]
	assert.Equal(t, "FIXED", *charge.Type) // taxType 0 = FIXED using constants

	// Test tax transformation using struct-based approach
	assert.NotNil(t, unifiedMenu.BillComponents.Taxes)
	assert.Len(t, unifiedMenu.BillComponents.Taxes, 1)
	tax := unifiedMenu.BillComponents.Taxes[0]
	assert.Equal(t, "LTtax1", *tax.ID)
	assert.Equal(t, "tax1", *tax.ProviderId)

	// Test that constants are used for food type transformation
	foodType := transformLimeTrayFoodType("1")
	assert.Equal(t, "veg", *foodType)

	foodType2 := transformLimeTrayFoodType("2")
	assert.Equal(t, "non veg", *foodType2)

	foodType3 := transformLimeTrayFoodType("unknown")
	assert.Equal(t, "not Specified", *foodType3)
}

func TestTransformUnifiedOrderToLimeTrayOrder(t *testing.T) {
	utilsImpl := utils.NewUtils()
	transformer := NewTransformers(utilsImpl)

	// Create a sample unified order
	order := &orders.Order{
		OrderInfo: orders.OrderInfo{
			OrderID: "test-order-123",
			Total:   500.0,
		},
	}

	// Transform order
	result := transformer.TransformUnifiedOrderToProviderOrder(order, constants.LimeTrayClientId)

	// Since LimeTray uses unified structure directly, it should return the same order
	assert.Equal(t, order, result)
}
