package service

import (
	"github.com/nutanalabs/pos-gateway/internal/constants"
	"github.com/nutanalabs/pos-gateway/internal/types"
	"go.mongodb.org/mongo-driver/bson"
)

func GetRestaurantIdFilterFromPosMenu(provider string, menu *map[string]interface{}) (bson.M, error) {
	switch provider {
	case constants.PetpoojaClientId:
		providerId := (*menu)["restaurants"].([]interface{})[0].(map[string]interface{})["restaurantid"].(string)
		return bson.M{
			"restaurants.restaurantid": providerId,
		}, nil
	case constants.UrbanpiperClientId:
		providerId := (*menu)["location"].(map[string]interface{})["ref_id"].(string)
		return bson.M{
			"location.ref_id": providerId,
		}, nil
	case constants.LimeTrayClientId:
		providerId := (*menu)["outletId"].(string)
		return bson.M{
			"outletId": providerId,
		}, nil
	}
	var providerId string
	providerMap, ok := (*menu)["restaurant"].(map[string]interface{})
	if !ok || providerMap["id"] == nil || providerMap["id"] == "" {
		return nil, &types.StatusError{
			DisplayMessage: "Invalid request",
			Message:        "Restaurant ID not found in menu for pushing to mongo",
		}
	} else {
		providerId = providerMap["id"].(string)
	}
	return bson.M{
		"restaurant.id": providerId,
	}, nil
}
