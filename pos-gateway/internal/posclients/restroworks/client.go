package restroworks

import (
	"github.com/nutanalabs/pos-gateway/internal/types/common"
	"net/http"
	"time"

	"github.com/nutanalabs/pos-gateway/internal/config"
	"github.com/nutanalabs/pos-gateway/internal/utils"
)

// Client represents the Restroworks API client
type Client struct {
	config      *config.Config
	httpClient  *http.Client
	utilsClient utils.HTTPClient
}

// NewClient creates a new Restroworks API client
func NewClient(cfg *config.Config, httpClient *http.Client, utilsClient utils.HTTPClient) *Client {
	return &Client{
		config:      cfg,
		httpClient:  httpClient,
		utilsClient: utilsClient,
	}
}

// SendOrder sends order details to Restroworks API
func (c *Client) SendOrder(order interface{}) (interface{}, error) {
	// For now, this is a mock implementation since we don't have the actual Restroworks API URL configured
	// In a real implementation, you would configure the Restroworks API URL in the config file
	// and make the actual HTTP request here

	// Mock successful response
	response := map[string]interface{}{
		"status":  "success",
		"message": "Order sent to Restroworks successfully",
		"order":   order,
	}

	return response, nil

	// Commented out the actual HTTP call until proper URL is configured:
	/*
		restroworksURL := "https://api.restroworks.com/v1/orders" // This should come from config
		res, err := c.utilsClient.Post(utils.HTTPPayload{
			Client:  c.httpClient,
			URL:     restroworksURL,
			Body:    order,
			Timeout: time.Second * 30,
		})
		if err != nil {
			return nil, err
		}
		return res, nil
	*/
}

// SendRiderDetails sends rider details to Restroworks API
func (c *Client) SendRiderDetails(rider interface{}) (interface{}, error) {
	res, err := c.utilsClient.Post(utils.HTTPPayload{
		Client: c.httpClient,
		Body:   rider,
	})
	if err != nil {
		return nil, err
	}
	return res, nil
}

// SendMenuProcessingRequestStatus sends menu processing status to Restroworks API
func (c *Client) SendMenuProcessingRequestStatus(status common.MenuProcessingStatus, url string) (interface{}, error) {
	res, err := c.utilsClient.Post(utils.HTTPPayload{
		URL:     url,
		Client:  c.httpClient,
		Body:    status,
		Timeout: time.Duration(30) * time.Second, // TODO: Make this configurable
		Headers: map[string]string{
			"Content-Type":  "application/json",
			"Authorization": "apikey sys_user_aggr_rapido:ec3567e69f2ea441fcf071334d1112201a40fd9d",
		},
	})
	if err != nil {
		return nil, err
	}
	return res, nil
}
