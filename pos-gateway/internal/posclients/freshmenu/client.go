package freshmenu

import (
	"github.com/nutanalabs/pos-gateway/internal/types/common"
	"net/http"
	"time"

	"github.com/nutanalabs/pos-gateway/internal/config"
	"github.com/nutanalabs/pos-gateway/internal/utils"
)

// Client represents the Freshmenu API client
type Client struct {
	config      *config.Config
	httpClient  *http.Client
	utilsClient utils.HTTPClient
}

// NewClient creates a new Freshmenu API client
func NewClient(cfg *config.Config, httpClient *http.Client, utilsClient utils.HTTPClient) *Client {
	return &Client{
		config:      cfg,
		httpClient:  httpClient,
		utilsClient: utilsClient,
	}
}

// SendOrder sends order details to Freshmenu API
func (c *Client) SendOrder(order interface{}) (interface{}, error) {
	// For now, this is a mock implementation since we don't have the actual Freshmenu API URL configured
	// In a real implementation, you would configure the Freshmenu API URL in the config file
	// and make the actual HTTP request here

	// Mock successful response
	response := map[string]interface{}{
		"status":  "success",
		"message": "Order sent to Freshmenu successfully",
	}

	return response, nil

	// Commented out the actual HTTP call until proper URL is configured:
	/*
		freshmenuURL := "https://api.freshmenu.com/v1/orders" // This should come from config
		res, err := c.utilsClient.Post(utils.HTTPPayload{
			Client:  c.httpClient,
			URL:     freshmenuURL,
			Body:    order,
			Timeout: time.Second * 30,
		})
		if err != nil {
			return nil, err
		}
		return res, nil
	*/
}

// SendRiderDetails sends rider details to Freshmenu API
func (c *Client) SendRiderDetails(rider interface{}) (interface{}, error) {
	res, err := c.utilsClient.Post(utils.HTTPPayload{
		Client: c.httpClient,
		Body:   rider,
	})
	if err != nil {
		return nil, err
	}
	return res, nil
}

// SendMenuProcessingRequestStatus sends menu processing status to Freshmenu API
func (c *Client) SendMenuProcessingRequestStatus(status common.MenuProcessingStatus, url string) (interface{}, error) {
	res, err := c.utilsClient.Post(utils.HTTPPayload{
		URL:     url,
		Client:  c.httpClient,
		Body:    status,
		Timeout: time.Duration(30) * time.Second, // TODO: Make this configurable
		Headers: map[string]string{
			"Content-Type":  "application/json",
			"Authorization": "apikey sys_user_aggr_rapido:ec3567e69f2ea441fcf071334d1112201a40fd9d",
		},
	})
	if err != nil {
		return nil, err
	}
	return res, nil
}
