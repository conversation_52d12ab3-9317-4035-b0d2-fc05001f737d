package constants

const (
	PetpoojaProvider    string = "PETPOOJA"
	UrbanpiperProvider  string = "URBANPIPER"
	RestroWorksProvider string = "RESTROWORKS"
	FreshMenuProvider   string = "FRESHMENU"
	LimeTrayProvider    string = "LIMETRAY"
)

const (
	PetpoojaClientId    string = "PETPOOJA"
	UrbanpiperClientId  string = "URBANPIPER"
	RestroWorksClientId string = "RESTROWORKS"
	FreshMenuClientId   string = "FRESHMENU"
	LimeTrayClientId    string = "LIMETRAY"
)

var ProviderAbbreviationMap = map[string]string{
	PetpoojaProvider:    "PP",
	UrbanpiperProvider:  "UP",
	RestroWorksProvider: "RW",
	FreshMenuProvider:   "FM",
	LimeTrayProvider:    "LT",
}

var FulfillmentModesId = map[string]int{
	"delivery": 1,
	"pickup":   2,
	"dinein":   3,
}

var PetPoojaFulfillmentModesMapToInternalMode = map[string]string{
	"Delivery": "delivery",
	"Pick Up":  "pickup",
	"Dine In":  "dinein",
}

var PetPoojaOrderTypesMap = map[int]string{
	1: "Delivery",
	2: "Pick Up",
	3: "Dine In",
}

var UrbanPiperFulfillmentModesMapToInternalMode = map[string]string{
	"delivery": "delivery",
	"pickup":   "pickup",
	"dinein":   "dinein",
}

var AllowedFoodTypes = []string{"veg", "non veg", "egg", "vegan", "not Specified"}
var AllowedChargeTypes = []string{"FIXED", "PERCENTAGE"}

var PetPoojaFoodTypesMapToInternal = map[string]string{
	"veg":     "veg",
	"non-veg": "non veg",
	"egg":     "egg",
}

var UrbanPiperFoodTypesMapToInternal = map[int]string{
	1: "veg",
	2: "non veg",
	3: "egg",
	4: "not Specified",
}

var LimeTrayFoodTypesMapToInternal = map[string]string{
	"1": "veg",
	"2": "non veg",
}

var LimeTrayServiceTypesMapToInternal = map[string]string{
	"1": "takeaway",
	"2": "delivery",
	"3": "dinein",
}

// LimeTray charge and tax type constants
const (
	LimeTrayTaxTypeFixed      = 0
	LimeTrayTaxTypePercentage = 1
)

// LimeTray charge type mappings
var LimeTrayChargeTypesMapToInternal = map[int]string{
	LimeTrayTaxTypeFixed:      "FIXED",
	LimeTrayTaxTypePercentage: "PERCENTAGE",
}

var ClientIDsEnabledForAsyncMenuProcessing = []string{FreshMenuClientId, UrbanpiperClientId, RestroWorksClientId}

// Payment types
const (
	PaymentTypeCash   = "cash"
	PaymentTypeCard   = "card"
	PaymentTypeUPI    = "upi"
	PaymentTypeOnline = "online"
)

// Delivery types
const (
	DeliveryTypeDelivery = "delivery"
	DeliveryTypeTakeaway = "takeaway"
	DeliveryTypeDineIn   = "dine-in"
)

// Order statuses
const (
	OrderStatusPlaced       = "placed"
	OrderStatusConfirmed    = "confirmed"
	OrderStatusAcknowledged = "acknowledged"
	OrderStatusFoodReady    = "food_ready"
	OrderStatusDispatched   = "dispatched"
	OrderStatusCompleted    = "completed"
	OrderStatusCancelled    = "cancelled"
)

// Fulfillment modes
const (
	FulfillmentModeDelivery = "delivery"
	FulfillmentModePickup   = "pickup"
)

// UrbanPiper specific fulfillment modes
const (
	UrbanPiperFulfillmentModeDelivery     = "delivery"
	UrbanPiperFulfillmentModePickup       = "pickup"
	UrbanPiperFulfillmentModeDeliverySelf = "delivery_self"
)

// Tax liability
const (
	TaxLiabilityVendor     = "vendor"
	TaxLiabilityAggregator = "aggregator"
	TaxLiabilityRestaurant = "restaurant"
)

// Event types
const (
	EventTypeOrderReceived = "order_received"
)

// Petpooja specific order types
const (
	PetpoojaOrderTypeDelivery = "H" // Home delivery
	PetpoojaOrderTypeTakeaway = "P" // Parcel (Takeaway)
	PetpoojaOrderTypeDineIn   = "D" // Dine-in
)

// Petpooja specific payment types
const (
	PetpoojaPaymentTypeCOD    = "COD"    // Cash on delivery
	PetpoojaPaymentTypeOnline = "ONLINE" // Online payment
)

// UrbanPiper specific payment modes
const (
	UrbanPiperPaymentModeCash   = "cash"
	UrbanPiperPaymentModeOnline = "online"
)

// Order status constants for transformations
const (
	OrderStatusPending = "pending"
)

// Day names for timing transformations
const (
	DayMonday    = "Monday"
	DayTuesday   = "Tuesday"
	DayWednesday = "Wednesday"
	DayThursday  = "Thursday"
	DayFriday    = "Friday"
	DaySaturday  = "Saturday"
	DaySunday    = "Sunday"
	DayAll       = "All"
)

// Short day names
const (
	DayMon = "Mon"
	DayTue = "Tue"
	DayWed = "Wed"
	DayThu = "Thu"
	DayFri = "Fri"
	DaySat = "Sat"
	DaySun = "Sun"
)

// Fulfillment mode constants for internal use
const (
	FulfillmentModeDeliveryInternal = "delivery"
	FulfillmentModePickupInternal   = "pickup"
	FulfillmentModeDineInInternal   = "dinein"
)

// Petpooja order type IDs (used in tax transformations)
const (
	PetpoojaOrderTypeIDDelivery = "1"
	PetpoojaOrderTypeIDPickup   = "2"
	PetpoojaOrderTypeIDDineIn   = "3"
	PetpoojaOrderTypeIDEmpty    = "0"
)

// Food type constants
const (
	FoodTypeNotSpecified = "not Specified"
)
