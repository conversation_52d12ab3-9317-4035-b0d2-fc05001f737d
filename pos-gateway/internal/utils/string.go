package utils

import (
	"strconv"
	"strings"
)

// StringPtr returns a pointer to the given string
func StringPtr(s string) *string {
	return &s
}

// Float64Ptr returns a pointer to the given float64
func Float64Ptr(f float64) *float64 {
	return &f
}

// SplitString splits a comma-separated string into a slice of strings
func SplitString(s *string) []string {
	if s == nil {
		return nil
	}
	return strings.Split(*s, ",")
}

// ParseIntWithDefaultValue parses a string to an int with a default value
func ParseIntWithDefaultValue(s *string, defaultValue int) int {
	if s == nil {
		return defaultValue
	}
	if i, err := strconv.Atoi(*s); err == nil {
		return i
	}
	return defaultValue
}
